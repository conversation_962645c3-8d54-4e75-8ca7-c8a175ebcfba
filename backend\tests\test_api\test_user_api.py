# backend/tests/test_user_api.py
"""
Tests for User API Routes

This module tests the REST API endpoints for user operations including authentication.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

# Mark all tests in this file
pytestmark = [
    pytest.mark.unit,
    pytest.mark.api,
    pytest.mark.integration,
    pytest.mark.user,
]

from api.v1.user_routes import router
from core.errors.exceptions import NotFoundError, BusinessLogicError


@pytest.fixture
def client():
    """Create a test client for the user router."""
    from fastapi import FastAPI

    app = FastAPI()
    app.include_router(router)
    return TestClient(app)


@pytest.fixture
def mock_user_service():
    """Create a mock user service."""
    return Mock()


class TestUserAuthenticationAPI:
    """Test user authentication API endpoints."""

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_login_success(self, mock_get_db, mock_service_class, client):
        """Test successful user login via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_user = Mock()
        mock_user.id = 1
        mock_user.name = "John Smith"
        mock_user.email = "<EMAIL>"
        mock_user.is_active = True
        # Add all required BaseSoftDeleteSchema fields
        mock_user.created_at = "2024-01-15T10:30:00Z"
        mock_user.updated_at = "2024-01-15T10:30:00Z"
        mock_user.is_deleted = False
        mock_user.deleted_at = None
        mock_user.deleted_by_user_id = None

        mock_login_response = Mock()
        mock_login_response.user = mock_user
        mock_login_response.access_token = "mock_token"
        mock_login_response.token_type = "bearer"

        mock_service.login.return_value = mock_login_response

        login_data = {
            "email": "<EMAIL>",
            "password": "SecurePass123",
        }

        # Act
        response = client.post("/users/login", json=login_data)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["access_token"] == "mock_token"
        assert data["token_type"] == "bearer"

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_login_invalid_credentials(self, mock_get_db, mock_service_class, client):
        """Test login with invalid credentials."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.login.side_effect = BusinessLogicError(
            code="INVALID_CREDENTIALS", detail="Invalid email or password"
        )

        login_data = {
            "email": "<EMAIL>",
            "password": "WrongPassword",
        }

        # Act
        response = client.post("/users/login", json=login_data)

        # Assert
        assert response.status_code == 401
        data = response.json()
        assert "Invalid" in data["detail"]

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_logout_success(self, mock_get_db, mock_service_class, client):
        """Test successful user logout via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_logout_response = Mock()
        mock_logout_response.message = "Logged out successfully"

        mock_service.logout.return_value = mock_logout_response

        # Act
        response = client.post("/users/logout?user_id=1")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "Logged out successfully" in data["message"]

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_change_password_success(self, mock_get_db, mock_service_class, client):
        """Test successful password change via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.change_password.return_value = True

        password_data = {
            "current_password": "OldPass123",
            "new_password": "NewSecurePass456",
        }

        # Act
        response = client.post("/users/change-password?user_id=1", json=password_data)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "Password changed successfully" in data["message"]

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_change_password_invalid_current(
        self, mock_get_db, mock_service_class, client
    ):
        """Test password change with invalid current password."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.change_password.side_effect = BusinessLogicError(
            code="INVALID_CURRENT_PASSWORD", detail="Current password is incorrect"
        )

        password_data = {
            "current_password": "WrongOldPass",
            "new_password": "NewSecurePass456",
        }

        # Act
        response = client.post("/users/change-password?user_id=1", json=password_data)

        # Assert
        assert response.status_code == 401
        data = response.json()
        assert "incorrect" in data["detail"]


class TestUserManagementAPI:
    """Test user management API endpoints."""

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_create_user_success(self, mock_get_db, mock_service_class, client):
        """Test successful user creation via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_user = Mock()
        mock_user.id = 1
        mock_user.name = "John Smith"
        mock_user.email = "<EMAIL>"
        mock_user.is_active = True
        # Add all required BaseSoftDeleteSchema fields
        mock_user.created_at = "2024-01-15T10:30:00Z"
        mock_user.updated_at = "2024-01-15T10:30:00Z"
        mock_user.is_deleted = False
        mock_user.deleted_at = None
        mock_user.deleted_by_user_id = None

        mock_service.create_user.return_value = mock_user

        user_data = {
            "name": "John Smith",
            "email": "<EMAIL>",
            "password": "SecurePass123",
            "is_active": True,
        }

        # Act
        response = client.post("/users", json=user_data)

        # Assert
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "John Smith"
        assert data["email"] == "<EMAIL>"

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_create_user_email_exists(self, mock_get_db, mock_service_class, client):
        """Test user creation when email already exists."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.create_user.side_effect = BusinessLogicError(
            code="EMAIL_ALREADY_EXISTS", detail="Email already exists"
        )

        user_data = {
            "name": "John Smith",
            "email": "<EMAIL>",
            "password": "SecurePass123",
        }

        # Act
        response = client.post("/users", json=user_data)

        # Assert
        assert response.status_code == 409
        data = response.json()
        assert "already exists" in data["detail"]

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_get_user_success(self, mock_get_db, mock_service_class, client):
        """Test successful user retrieval via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_user = Mock()
        mock_user.id = 1
        mock_user.name = "John Smith"
        mock_user.email = "<EMAIL>"
        mock_user.is_active = True
        # Add all required BaseSoftDeleteSchema fields
        mock_user.created_at = "2024-01-15T10:30:00Z"
        mock_user.updated_at = "2024-01-15T10:30:00Z"
        mock_user.is_deleted = False
        mock_user.deleted_at = None
        mock_user.deleted_by_user_id = None

        mock_service.get_user.return_value = mock_user

        # Act
        response = client.get("/users/1")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "John Smith"

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_get_user_not_found(self, mock_get_db, mock_service_class, client):
        """Test user retrieval when not found."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.get_user.side_effect = NotFoundError(
            code="USER_NOT_FOUND", detail="User 999 not found"
        )

        # Act
        response = client.get("/users/999")

        # Assert
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"]

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_list_users_success(self, mock_get_db, mock_service_class, client):
        """Test successful user listing via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_users = [Mock(), Mock()]
        mock_service.get_users.return_value = mock_users
        mock_service.count_active_users.return_value = 2

        # Act
        response = client.get("/users")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "users" in data
        assert data["total"] == 2

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_list_users_with_search(self, mock_get_db, mock_service_class, client):
        """Test user listing with search via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_users = [Mock()]
        mock_service.search_users.return_value = mock_users

        # Act
        response = client.get("/users?search=john")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "users" in data
        mock_service.search_users.assert_called_with("john", 0, 10)

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_update_user_success(self, mock_get_db, mock_service_class, client):
        """Test successful user update via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_updated = Mock()
        mock_updated.id = 1
        mock_updated.name = "Updated Name"

        mock_service.update_user.return_value = mock_updated

        update_data = {
            "name": "Updated Name",
        }

        # Act
        response = client.put("/users/1", json=update_data)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Name"

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_deactivate_user_success(self, mock_get_db, mock_service_class, client):
        """Test successful user deactivation via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.deactivate_user.return_value = True

        # Act
        response = client.delete("/users/1")

        # Assert
        assert response.status_code == 204


class TestUserPreferencesAPI:
    """Test user preferences API endpoints."""

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_get_user_preferences_success(
        self, mock_get_db, mock_service_class, client
    ):
        """Test successful user preferences retrieval via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_preferences = Mock()
        mock_preferences.user_id = 1
        mock_preferences.ui_theme = "dark"

        mock_service.get_user_preferences.return_value = mock_preferences

        # Act
        response = client.get("/users/1/preferences")

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["ui_theme"] == "dark"

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_get_user_preferences_not_found(
        self, mock_get_db, mock_service_class, client
    ):
        """Test user preferences retrieval when not found."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.get_user_preferences.return_value = None

        # Act
        response = client.get("/users/1/preferences")

        # Assert
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"]

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_update_user_preferences_success(
        self, mock_get_db, mock_service_class, client
    ):
        """Test successful user preferences update via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_preferences = Mock()
        mock_preferences.user_id = 1
        mock_preferences.ui_theme = "light"

        mock_service.create_or_update_user_preferences.return_value = mock_preferences

        preferences_data = {
            "ui_theme": "light",
            "default_safety_margin_percent": 15.0,
        }

        # Act
        response = client.put("/users/1/preferences", json=preferences_data)

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["ui_theme"] == "light"

    @patch("api.v1.user_routes.UserService")
    @patch("api.v1.user_routes.get_db_session")
    def test_delete_user_preferences_success(
        self, mock_get_db, mock_service_class, client
    ):
        """Test successful user preferences deletion via API."""
        # Arrange
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        mock_service.delete_user_preferences.return_value = True

        # Act
        response = client.delete("/users/1/preferences")

        # Assert
        assert response.status_code == 204
