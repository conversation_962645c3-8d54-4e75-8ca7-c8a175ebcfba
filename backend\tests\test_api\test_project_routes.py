# backend/tests/test_api/test_project_routes.py
"""
Integration tests for Project API routes.

Tests the complete API functionality including request/response handling,
validation, error responses, and integration with the service layer.
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI

# Mark all tests in this file
pytestmark = [
    pytest.mark.unit,
    pytest.mark.api,
    pytest.mark.integration,
    pytest.mark.project,
]

from api.v1.project_routes import router as project_router
from core.services.project_service import ProjectService
from core.schemas.project_schemas import (
    ProjectReadSchema,
    ProjectListResponseSchema,
    ProjectSummarySchema,
)
from core.errors.exceptions import (
    ProjectNotFoundError,
    DuplicateEntryError,
    DataValidationError,
)


@pytest.fixture
def app():
    """Create a FastAPI app for testing."""
    app = FastAPI()
    app.include_router(project_router, prefix="/api/v1/projects")
    return app


@pytest.fixture
def client(app):
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_project_service():
    """Create a mock ProjectService."""
    return Mock(spec=ProjectService)


@pytest.fixture
def sample_project_response_data():
    """Sample project response data."""
    return {
        "id": 1,
        "name": "Test Heat Tracing Project",
        "project_number": "HT-TEST-001",
        "description": "A test project for heat tracing design",
        "designer": "Test Engineer",
        "notes": "Test project notes",
        "min_ambient_temp_c": -20.0,
        "max_ambient_temp_c": 40.0,
        "desired_maintenance_temp_c": 65.0,
        "wind_speed_ms": 5.0,
        "installation_environment": "outdoor",
        "available_voltages_json": "[120, 240, 480]",
        "default_cable_manufacturer": "Test Manufacturer",
        "default_control_device_manufacturer": "Test Control Manufacturer",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "is_deleted": False,
        "deleted_at": None,
        "deleted_by_user_id": None,
    }


class TestProjectRoutes:
    """Test Project API routes."""

    def test_create_project_success(self, client):
        """Test successful project creation using dependency override."""
        from api.v1.project_routes import get_project_service

        # Create mock service
        mock_service = Mock()

        # Create complete mock project with all required fields
        mock_project = Mock()
        mock_project.id = 1
        mock_project.name = "Test Heat Tracing Project"
        mock_project.project_number = "HT-TEST-001"
        mock_project.description = "A test project for heat tracing design"
        mock_project.designer = "Test Engineer"
        mock_project.notes = "Test project notes"
        mock_project.min_ambient_temp_c = -20.0
        mock_project.max_ambient_temp_c = 40.0
        mock_project.desired_maintenance_temp_c = 65.0
        mock_project.wind_speed_ms = 5.0
        mock_project.installation_environment = "outdoor"
        mock_project.available_voltages_json = "[120, 240, 480]"
        mock_project.default_cable_manufacturer = "Test Manufacturer"
        mock_project.default_control_device_manufacturer = "Test Control Manufacturer"
        mock_project.created_at = "2024-01-15T10:30:00Z"
        mock_project.updated_at = "2024-01-15T10:30:00Z"
        mock_project.is_deleted = False
        mock_project.deleted_at = None
        mock_project.deleted_by_user_id = None

        mock_service.create_project.return_value = mock_project

        # Override the dependency
        def override_get_project_service():
            return mock_service

        client.app.dependency_overrides[get_project_service] = (
            override_get_project_service
        )

        project_data = {
            "name": "Test Heat Tracing Project",
            "project_number": "HT-TEST-001",
            "description": "A test project for heat tracing design",
            "designer": "Test Engineer",
            "notes": "Test project notes",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
            "wind_speed_ms": 5.0,
            "installation_environment": "outdoor",
            "available_voltages_json": "[120, 240, 480]",
            "default_cable_manufacturer": "Test Manufacturer",
            "default_control_device_manufacturer": "Test Control Manufacturer",
        }

        try:
            # Act
            response = client.post("/api/v1/projects/", json=project_data)

            # Assert
            assert response.status_code == 201
            data = response.json()
            assert data["name"] == "Test Heat Tracing Project"
            assert data["project_number"] == "HT-TEST-001"
        finally:
            # Clean up dependency override
            if get_project_service in client.app.dependency_overrides:
                del client.app.dependency_overrides[get_project_service]

    @patch("api.v1.project_routes.get_project_service")
    def test_create_project_validation_error(
        self, mock_get_service, client, mock_project_service
    ):
        """Test project creation with validation error."""
        # Setup mock
        mock_get_service.return_value = mock_project_service

        # Test data with validation errors
        invalid_data = {
            "name": "",  # Invalid: empty name
            "project_number": "HT-TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }

        # Execute
        response = client.post("/api/v1/projects/", json=invalid_data)

        # Verify
        assert response.status_code == 422  # Validation error
        response_data = response.json()
        assert "detail" in response_data

    @patch("api.v1.project_routes.get_project_service")
    def test_create_project_duplicate_error(
        self, mock_get_service, client, mock_project_service
    ):
        """Test project creation with duplicate error."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        mock_project_service.create_project.side_effect = DuplicateEntryError(
            message="A project with the name 'Test Project' already exists."
        )

        # Test data
        create_data = {
            "name": "Test Heat Tracing Project",
            "project_number": "HT-TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }

        # Execute
        response = client.post("/api/v1/projects/", json=create_data)

        # Verify
        assert response.status_code == 409  # Conflict
        response_data = response.json()
        assert "already exists" in response_data["detail"]

    @patch("api.v1.project_routes.get_project_service")
    def test_get_project_by_id_success(
        self,
        mock_get_service,
        client,
        mock_project_service,
        sample_project_response_data,
    ):
        """Test successful project retrieval by ID."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        mock_project_service.get_project_details.return_value = ProjectReadSchema(
            **sample_project_response_data
        )

        # Execute
        response = client.get("/api/v1/projects/1")

        # Verify
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == 1
        assert response_data["name"] == sample_project_response_data["name"]

    @patch("api.v1.project_routes.get_project_service")
    def test_get_project_by_code_success(
        self,
        mock_get_service,
        client,
        mock_project_service,
        sample_project_response_data,
    ):
        """Test successful project retrieval by project number."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        mock_project_service.get_project_details.return_value = ProjectReadSchema(
            **sample_project_response_data
        )

        # Execute
        response = client.get("/api/v1/projects/HT-TEST-001")

        # Verify
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["project_number"] == "HT-TEST-001"

    @patch("api.v1.project_routes.get_project_service")
    def test_get_project_not_found(
        self, mock_get_service, client, mock_project_service
    ):
        """Test project retrieval when project doesn't exist."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        mock_project_service.get_project_details.side_effect = ProjectNotFoundError(
            "999"
        )

        # Execute
        response = client.get("/api/v1/projects/999")

        # Verify
        assert response.status_code == 404
        response_data = response.json()
        assert "not found" in response_data["detail"].lower()

    @patch("api.v1.project_routes.get_project_service")
    def test_update_project_success(
        self,
        mock_get_service,
        client,
        mock_project_service,
        sample_project_response_data,
    ):
        """Test successful project update."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        updated_data = sample_project_response_data.copy()
        updated_data["name"] = "Updated Project Name"
        mock_project_service.update_project.return_value = ProjectReadSchema(
            **updated_data
        )

        # Test data
        update_data = {
            "name": "Updated Project Name",
            "description": "Updated description",
        }

        # Execute
        response = client.put("/api/v1/projects/1", json=update_data)

        # Verify
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["name"] == "Updated Project Name"

    @patch("api.v1.project_routes.get_project_service")
    def test_update_project_not_found(
        self, mock_get_service, client, mock_project_service
    ):
        """Test project update when project doesn't exist."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        mock_project_service.update_project.side_effect = ProjectNotFoundError("999")

        # Test data
        update_data = {"name": "Updated Name"}

        # Execute
        response = client.put("/api/v1/projects/999", json=update_data)

        # Verify
        assert response.status_code == 404

    @patch("api.v1.project_routes.get_project_service")
    def test_delete_project_success(
        self, mock_get_service, client, mock_project_service
    ):
        """Test successful project deletion."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        mock_project_service.delete_project.return_value = None

        # Execute
        response = client.delete("/api/v1/projects/1")

        # Verify
        assert response.status_code == 204
        mock_project_service.delete_project.assert_called_once_with(
            "1", deleted_by_user_id=None
        )

    @patch("api.v1.project_routes.get_project_service")
    def test_delete_project_not_found(
        self, mock_get_service, client, mock_project_service
    ):
        """Test project deletion when project doesn't exist."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        mock_project_service.delete_project.side_effect = ProjectNotFoundError("999")

        # Execute
        response = client.delete("/api/v1/projects/999")

        # Verify
        assert response.status_code == 404

    @patch("api.v1.project_routes.get_project_service")
    def test_list_projects_success(
        self, mock_get_service, client, mock_project_service
    ):
        """Test successful project listing."""
        # Setup mock data
        project_summaries = []
        for i in range(5):
            summary_data = {
                "id": i + 1,
                "name": f"Test Project {i + 1}",
                "project_number": f"TEST-{i + 1:03d}",
                "description": f"Description {i + 1}",
                "designer": f"Engineer {i + 1}",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
            }
            project_summaries.append(ProjectSummarySchema(**summary_data))

        list_response = ProjectListResponseSchema(
            projects=project_summaries, total=25, page=1, per_page=10, total_pages=3
        )

        mock_get_service.return_value = mock_project_service
        mock_project_service.get_projects_list.return_value = list_response

        # Execute
        response = client.get("/api/v1/projects/")

        # Verify
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["projects"]) == 5
        assert response_data["total"] == 25
        assert response_data["page"] == 1
        assert response_data["per_page"] == 10
        assert response_data["total_pages"] == 3

    @patch("api.v1.project_routes.get_project_service")
    def test_list_projects_with_pagination(
        self, mock_get_service, client, mock_project_service
    ):
        """Test project listing with custom pagination."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        list_response = ProjectListResponseSchema(
            projects=[], total=0, page=2, per_page=5, total_pages=1
        )
        mock_project_service.get_projects_list.return_value = list_response

        # Execute
        response = client.get("/api/v1/projects/?page=2&per_page=5")

        # Verify
        assert response.status_code == 200
        mock_project_service.get_projects_list.assert_called_once_with(
            page=2, per_page=5, include_deleted=False
        )

    @patch("api.v1.project_routes.get_project_service")
    def test_list_projects_include_deleted(
        self, mock_get_service, client, mock_project_service
    ):
        """Test project listing including deleted projects."""
        # Setup mock
        mock_get_service.return_value = mock_project_service
        list_response = ProjectListResponseSchema(
            projects=[], total=0, page=1, per_page=10, total_pages=1
        )
        mock_project_service.get_projects_list.return_value = list_response

        # Execute
        response = client.get("/api/v1/projects/?include_deleted=true")

        # Verify
        assert response.status_code == 200
        mock_project_service.get_projects_list.assert_called_once_with(
            page=1, per_page=10, include_deleted=True
        )

    def test_invalid_pagination_parameters(self, client):
        """Test API validation for invalid pagination parameters."""
        # Test negative page
        response = client.get("/api/v1/projects/?page=-1")
        assert response.status_code == 422

        # Test zero page
        response = client.get("/api/v1/projects/?page=0")
        assert response.status_code == 422

        # Test per_page too large
        response = client.get("/api/v1/projects/?per_page=101")
        assert response.status_code == 422

        # Test per_page too small
        response = client.get("/api/v1/projects/?per_page=0")
        assert response.status_code == 422
